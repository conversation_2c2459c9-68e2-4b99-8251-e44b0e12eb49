document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('codeInput');
    const outputDisplay = document.getElementById('outputDisplay');
    const outputType = document.getElementById('outputType');
    const clearBtn = document.getElementById('clearBtn');
    const analyzeBtn = document.getElementById('analyzeBtn');

    // Clear button functionality
    clearBtn.addEventListener('click', function() {
        codeInput.value = '';
        showPlaceholder();
    });

    // Analyze button functionality
    analyzeBtn.addEventListener('click', function() {
        const code = codeInput.value.trim();
        if (!code) {
            alert('Please enter some C code to analyze');
            return;
        }
        
        generateOutput(code, outputType.value);
    });

    // Output type selector change
    outputType.addEventListener('change', function() {
        const code = codeInput.value.trim();
        if (code) {
            generateOutput(code, outputType.value);
        }
    });

    function showPlaceholder() {
        outputDisplay.innerHTML = `
            <div class="placeholder">
                <p>Enter C code and click "Analyze Code" to see the results</p>
                <div class="analysis-info">
                    <h3>Analysis Types:</h3>
                    <ul>
                        <li><strong>Lexical Analysis:</strong> Breaks code into tokens (keywords, identifiers, operators, etc.)</li>
                        <li><strong>AST:</strong> Shows the hierarchical structure of your code</li>
                        <li><strong>Flowchart:</strong> Visual representation of program flow</li>
                    </ul>
                </div>
            </div>
        `;
    }

    function generateOutput(code, type) {
        let output = '';
        
        switch(type) {
            case 'lexical':
                output = generateLexicalAnalysis(code);
                break;
            case 'ast':
                output = generateAST(code);
                break;
            case 'flowchart':
                output = generateFlowchart(code);
                break;
        }
        
        outputDisplay.innerHTML = `<div class="output-content">${output}</div>`;
    }

    function generateLexicalAnalysis(code) {
        return `Lexical Analysis Results:
========================

[This is a placeholder for lexical analysis]

Input Code:
${code}

Tokens would be identified here:
- Keywords: #include, int, return, etc.
- Identifiers: main, printf, variables, etc.
- Operators: =, +, etc.
- Literals: numbers, strings, etc.
- Delimiters: {}, (), ;, etc.

Note: Actual lexical analysis logic needs to be implemented.`;
    }

    function generateAST(code) {
        return `Abstract Syntax Tree (AST):
============================

[This is a placeholder for AST generation]

Input Code:
${code}

AST Structure would be shown here:
Program
├── Include Directive
│   └── stdio.h
├── Function Declaration
│   ├── Return Type: int
│   ├── Name: main
│   ├── Parameters: ()
│   └── Body
│       ├── Variable Declarations
│       ├── Statements
│       └── Return Statement

Note: Actual AST generation logic needs to be implemented.`;
    }

    function generateFlowchart(code) {
        return `Flowchart Representation:
=========================

[This is a placeholder for flowchart generation]

Input Code:
${code}

Flowchart would be displayed here:
┌─────────────┐
│    START    │
└─────┬───────┘
      │
┌─────▼───────┐
│ Include     │
│ Libraries   │
└─────┬───────┘
      │
┌─────▼───────┐
│ Declare     │
│ Variables   │
└─────┬───────┘
      │
┌─────▼───────┐
│ Process     │
│ Logic       │
└─────┬───────┘
      │
┌─────▼───────┐
│    END      │
└─────────────┘

Note: Actual flowchart generation logic needs to be implemented.`;
    }

    // Initialize with placeholder
    showPlaceholder();
});

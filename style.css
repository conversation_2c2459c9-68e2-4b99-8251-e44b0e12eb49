* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5em;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.editor-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: 70vh;
}

.input-section, .output-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.input-section h2, .output-section h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

#codeInput {
    flex: 1;
    border: 2px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: none;
    background-color: #fafafa;
    transition: border-color 0.3s ease;
}

#codeInput:focus {
    outline: none;
    border-color: #3498db;
    background-color: white;
}

.input-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

#clearBtn {
    background-color: #e74c3c;
    color: white;
}

#clearBtn:hover {
    background-color: #c0392b;
}

#analyzeBtn {
    background-color: #3498db;
    color: white;
    flex: 1;
}

#analyzeBtn:hover {
    background-color: #2980b9;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.selector-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.selector-container label {
    font-weight: 600;
    color: #2c3e50;
}

#outputType {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
    cursor: pointer;
}

#outputType:focus {
    outline: none;
    border-color: #3498db;
}

.output-display {
    flex: 1;
    border: 2px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    background-color: #fafafa;
    overflow-y: auto;
}

.placeholder {
    text-align: center;
    color: #7f8c8d;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.placeholder p {
    font-size: 1.2em;
    margin-bottom: 30px;
}

.analysis-info {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    text-align: left;
    max-width: 400px;
}

.analysis-info h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

.analysis-info ul {
    list-style: none;
}

.analysis-info li {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #3498db;
}

.analysis-info strong {
    color: #2c3e50;
}

.output-content {
    background: white;
    padding: 20px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    border: 1px solid #ddd;
}

@media (max-width: 768px) {
    .editor-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .input-section, .output-section {
        min-height: 400px;
    }
    
    .output-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
